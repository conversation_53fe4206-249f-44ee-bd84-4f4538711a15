import React, { useState, useEffect } from "react";
import { showErrorToast } from "@/lib/utils/toast-utils";

// Generic hook for teacher analytics API calls
export function useTeacherAnalytics<T>(
  endpoint: string,
  dependencies: React.DependencyList = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!endpoint) {
        setError("Endpoint không hợp lệ");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await fetch(endpoint);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || "Failed to load data");
        }

        setData(result.data);
      } catch (err) {
        console.error(`Error fetching data from ${endpoint}:`, err);
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Không thể tải dữ liệu. Vui lòng thử lại sau.";
        setError(errorMessage);
        showErrorToast("Không thể tải dữ liệu");
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if we have a valid endpoint
    if (endpoint) {
      fetchData();
    } else {
      setLoading(false);
      setData(null);
      setError(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [endpoint, ...dependencies]);

  const refetch = React.useCallback(() => {
    if (!endpoint) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(endpoint);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.error || "Failed to load data");
        }

        setData(result.data);
      } catch (err) {
        console.error(`Error refetching data from ${endpoint}:`, err);
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Không thể tải dữ liệu. Vui lòng thử lại sau.";
        setError(errorMessage);
        showErrorToast("Không thể tải dữ liệu");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [endpoint]);

  return { data, loading, error, refetch };
}

// Type definitions for API responses
interface TeacherAnalyticsData {
  quiz_info: {
    quiz_id: number;
    name: string;
    total_questions: number;
  };
  overall_performance: {
    total_participants: number;
    average_score: number;
    completion_rate: number;
  };
  learning_outcome_analysis: Array<{
    lo_name: string;
    accuracy: number;
    performance_level: "excellent" | "good" | "average" | "weak";
    insights: string[];
    recommendations: string[];
  }>;
  student_groups: {
    excellent: { count: number; percentage: number };
    good: { count: number; percentage: number };
    average: { count: number; percentage: number };
    weak: { count: number; percentage: number };
  };
  teacher_insights: Array<{
    category: "strengths" | "weaknesses" | "recommendations";
    message: string;
    priority: "maintain" | "improve" | "urgent";
  }>;
}

interface StudentGroupAnalysisData {
  group_overview: {
    group_name: "excellent" | "good" | "average" | "weak";
    student_count: number;
    score_range: { min: number; max: number; average: number };
    insights: string[];
    recommendations: Array<{
      type: "immediate_action" | "long_term" | "monitoring";
      suggestion: string;
      priority: "high" | "medium" | "low";
    }>;
  };
  students: Array<{
    user_id: number;
    name: string;
    score: number;
    correct_answers: number;
  }>;
  learning_outcome_analysis: Array<{
    lo_name: string;
    accuracy: number;
    performance_level: "excellent" | "good" | "average" | "weak";
    insights: string[];
  }>;
  difficulty_level_analysis: Array<{
    level: string;
    accuracy: number;
    question_count: number;
    insights: string[];
  }>;
}

interface TeachingInsightsData {
  summary_insights: {
    overall_assessment: "excellent" | "good" | "mixed" | "needs_improvement";
    key_strengths: string[];
    main_challenges: string[];
    immediate_actions_needed: number;
  };
  detailed_insights: {
    curriculum_insights: Array<{
      type: "strength" | "weakness" | "opportunity";
      message: string;
      impact: "high" | "medium" | "low";
    }>;
    teaching_method_insights: Array<{
      type: "strength" | "weakness" | "opportunity";
      message: string;
      impact: "high" | "medium" | "low";
    }>;
    student_insights: Array<{
      type: "strength" | "weakness" | "opportunity";
      message: string;
      impact: "high" | "medium" | "low";
    }>;
    action_recommendations: Array<{
      category: "curriculum_revision" | "teaching_method" | "student_support";
      action: string;
      priority: "high" | "medium" | "low";
      timeline: "immediate" | "short_term" | "long_term";
    }>;
  };
}

interface QuizBenchmarkData {
  current_quiz: {
    quiz_id: number;
    name: string;
    metrics: {
      average_score: number;
      completion_rate: number;
      pass_rate: number;
      excellence_rate: number;
    };
  };
  comparisons: {
    subject_benchmark: {
      comparison_base: string;
      subject_average: {
        average_score: number;
        completion_rate: number;
      };
      current_vs_average: {
        score_difference: number;
        completion_difference: number;
      };
    };
  };
  performance_ranking: {
    current_rank: number;
    total_quizzes: number;
    percentile: number;
    ranking_insights: string;
  };
  insights: Array<{
    type: "positive" | "negative" | "neutral";
    category: "performance" | "participation" | "improvement";
    message: string;
  }>;
  recommendations: Array<{
    category: "improvement" | "maintenance" | "investigation";
    suggestion: string;
    priority: "high" | "medium" | "low";
  }>;
}

// Specific hooks for each analytics type
export function useComprehensiveQuizReport(quizId: number) {
  const endpoint = quizId
    ? `/api/teacher-analytics/quiz/${quizId}/comprehensive-report`
    : "";

  return useTeacherAnalytics<TeacherAnalyticsData>(endpoint, [quizId]);
}

export function useStudentGroupAnalysis(quizId: number, groupType: string) {
  const endpoint =
    quizId && groupType
      ? `/api/teacher-analytics/quiz/${quizId}/student-groups/${groupType}`
      : "";

  return useTeacherAnalytics<StudentGroupAnalysisData>(endpoint, [
    quizId,
    groupType,
  ]);
}

export function useTeachingInsights(quizId: number) {
  const endpoint = quizId
    ? `/api/teacher-analytics/quiz/${quizId}/teaching-insights`
    : "";

  return useTeacherAnalytics<TeachingInsightsData>(endpoint, [quizId]);
}

export function useQuizBenchmark(
  quizId: number,
  options?: {
    compareWithSubject?: boolean;
    compareWithTeacher?: boolean;
  }
) {
  const params = new URLSearchParams();
  if (options?.compareWithSubject !== undefined) {
    params.set("compare_with_subject", String(options.compareWithSubject));
  }
  if (options?.compareWithTeacher !== undefined) {
    params.set("compare_with_teacher", String(options.compareWithTeacher));
  }

  const endpoint = quizId
    ? `/api/teacher-analytics/quiz/${quizId}/benchmark?${params}`
    : "";

  // Use stable values for dependencies instead of the options object
  return useTeacherAnalytics<QuizBenchmarkData>(endpoint, [
    quizId,
    options?.compareWithSubject,
    options?.compareWithTeacher,
  ]);
}
