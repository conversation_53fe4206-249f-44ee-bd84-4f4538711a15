"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/components/ui/layout";
import { But<PERSON> } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Progress } from "@/components/ui/feedback";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/forms";
import {
  Loader2,
  Users,
  Target,
  AlertCircle,
  CheckCircle,
  BookOpen,
  Award,
  BarChart3,
  Eye,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { useStudentGroupAnalysis } from "@/hooks/useTeacherAnalytics";

interface StudentGroupChapterAnalysisProps {
  quizId: number;
  className?: string;
}

export default function StudentGroupChapterAnalysis({
  quizId,
  className = "",
}: StudentGroupChapterAnalysisProps) {
  const [selectedGroup, setSelectedGroup] = useState<string>("excellent");
  const [expandedStudents, setExpandedStudents] = useState(false);

  const { data, loading, error, refetch } = useStudentGroupAnalysis(
    quizId,
    selectedGroup
  );

  // Helper functions
  const getGroupColor = (groupName: string) => {
    switch (groupName) {
      case "excellent":
        return "bg-green-100 text-green-800 border-green-200";
      case "good":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "average":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "weak":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getGroupLabel = (groupName: string) => {
    switch (groupName) {
      case "excellent":
        return "Xuất sắc";
      case "good":
        return "Giỏi";
      case "average":
        return "Trung bình";
      case "weak":
        return "Yếu";
      default:
        return groupName;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "immediate_action":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "long_term":
        return <Target className="h-4 w-4 text-blue-600" />;
      case "monitoring":
        return <Eye className="h-4 w-4 text-yellow-600" />;
      default:
        return <CheckCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-20">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <span className="text-lg font-medium text-muted-foreground">
              Đang tải dữ liệu phân tích nhóm...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-20">
          <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-600 mb-2">
            Lỗi tải dữ liệu
          </p>
          <p className="text-muted-foreground text-center">{error}</p>
          <Button onClick={refetch} className="mt-4" variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Group Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            Phân tích nhóm học sinh
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <label className="text-sm font-medium">Chọn nhóm:</label>
            <Select value={selectedGroup} onValueChange={setSelectedGroup}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Chọn nhóm học sinh" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excellent">Xuất sắc</SelectItem>
                <SelectItem value="good">Giỏi</SelectItem>
                <SelectItem value="average">Trung bình</SelectItem>
                <SelectItem value="weak">Yếu</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {data && (
            <div
              className={`p-4 rounded-lg border-2 ${getGroupColor(
                data.group_overview.group_name
              )}`}
            >
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold">
                    Nhóm {getGroupLabel(data.group_overview.group_name)}
                  </h3>
                  <p className="text-sm opacity-80">
                    {data.group_overview.student_count} học sinh
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {data.group_overview.score_range.average.toFixed(1)}
                  </div>
                  <div className="text-sm opacity-80">
                    Điểm TB ({data.group_overview.score_range.min.toFixed(1)} -{" "}
                    {data.group_overview.score_range.max.toFixed(1)})
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {data && (
        <>
          {/* Group Insights */}
          {data.group_overview.insights.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-6 w-6 text-primary" />
                  Insights về nhóm
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {data.group_overview.insights.map((insight, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg"
                    >
                      <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-blue-800">{insight}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {data.group_overview.recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-6 w-6 text-primary" />
                  Khuyến nghị cho nhóm
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.group_overview.recommendations.map((rec, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-4 border rounded-lg"
                    >
                      <div className="flex-shrink-0 mt-1">
                        {getTypeIcon(rec.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-sm">
                            {rec.type === "immediate_action" &&
                              "Hành động ngay"}
                            {rec.type === "long_term" && "Dài hạn"}
                            {rec.type === "monitoring" && "Theo dõi"}
                          </span>
                          <Badge className={getPriorityColor(rec.priority)}>
                            {rec.priority === "high" && "Cao"}
                            {rec.priority === "medium" && "Trung bình"}
                            {rec.priority === "low" && "Thấp"}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {rec.suggestion}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Students List */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-6 w-6 text-primary" />
                  Danh sách học sinh ({data.students.length})
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setExpandedStudents(!expandedStudents)}
                  className="flex items-center gap-2"
                >
                  {expandedStudents ? (
                    <>
                      <ChevronUp className="h-4 w-4" />
                      Thu gọn
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-4 w-4" />
                      Xem chi tiết
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            {expandedStudents && (
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {data.students.map((student, index) => (
                    <div
                      key={student.user_id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{student.name}</p>
                          <p className="text-sm text-muted-foreground">
                            ID: {student.user_id}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-primary">
                          {student.score.toFixed(1)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {student.correct_answers} câu đúng
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>

          {/* Learning Outcome Analysis */}
          {data.learning_outcome_analysis.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-6 w-6 text-primary" />
                  Phân tích Learning Outcomes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.learning_outcome_analysis.map((lo, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-semibold">{lo.lo_name}</h4>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-muted-foreground">
                              Độ chính xác: {(lo.accuracy * 100).toFixed(1)}%
                            </span>
                            <Badge
                              className={getGroupColor(lo.performance_level)}
                            >
                              {getGroupLabel(lo.performance_level)}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <Progress
                            value={lo.accuracy * 100}
                            className="w-24 h-2"
                          />
                        </div>
                      </div>

                      {lo.insights.length > 0 && (
                        <div>
                          <h5 className="font-medium text-sm mb-2">
                            Insights:
                          </h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {lo.insights.map((insight, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {insight}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Difficulty Level Analysis */}
          {data.difficulty_level_analysis.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-6 w-6 text-primary" />
                  Phân tích theo độ khó
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.difficulty_level_analysis.map((level, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-semibold">
                            Mức độ {level.level}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-muted-foreground">
                              {level.question_count} câu hỏi - Độ chính xác:{" "}
                              {(level.accuracy * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <Progress
                            value={level.accuracy * 100}
                            className="w-24 h-2"
                          />
                        </div>
                      </div>

                      {level.insights.length > 0 && (
                        <div>
                          <h5 className="font-medium text-sm mb-2">
                            Insights:
                          </h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {level.insights.map((insight, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <span className="text-orange-500 mt-1">•</span>
                                {insight}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
}
