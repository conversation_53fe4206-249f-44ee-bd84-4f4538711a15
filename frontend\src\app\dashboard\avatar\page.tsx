"use client";

import React, { useState } from "react";
import {
  B<PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/layout";

import { useAvatar } from "@/lib/hooks/use-avatar";
import {
  ConnectedAvatarDisplay,
  CustomizationTabs,
  AvatarGrid,
  type CustomizationTab,
} from "@/components/features/avatar";
import { Loader2, Palette, Frame, Sparkles } from "lucide-react";

// Props interface for the page
interface AvatarCustomizationPageProps {}

/**
 * Avatar Customization Page Component
 *
 * Main page for avatar customization interface allowing users to:
 * - Browse and equip avatars
 * - Browse and equip frames
 * - Browse and equip name effects
 * - Preview combinations before equipping
 */
const AvatarCustomizationPage: React.FC<AvatarCustomizationPageProps> = () => {
  // State management
  const [activeTab, setActiveTab] = useState<CustomizationTab>("avatars");

  // Avatar hook for data and actions
  const {
    myAvatarData,
    availableItems,
    isLoading,
    isAvailableItemsLoading,
    isEquipping,
    error,
    equippedAvatar,
    equippedFrame,
    totalItems,
    completionRate,
    equipItem,
  } = useAvatar();

  // Handle avatar equip
  const handleEquipAvatar = async (avatarId: number) => {
    try {
      await equipItem({ itemType: "avatar", itemId: avatarId });
    } catch (error) {
      console.error("Failed to equip avatar:", error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Đang tải dữ liệu avatar...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Lỗi tải dữ liệu</CardTitle>
            <CardDescription>
              Không thể tải dữ liệu avatar. Vui lòng thử lại sau.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Tùy chỉnh Avatar</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Tùy chỉnh Avatar
          </h1>
          <p className="text-muted-foreground">
            Thay đổi avatar, khung và hiệu ứng tên để thể hiện phong cách của
            bạn
          </p>
        </div>

        {/* Current Avatar Preview */}
        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm font-medium">Avatar hiện tại</p>
            <p className="text-xs text-muted-foreground">
              {totalItems} items • {completionRate}% hoàn thành
            </p>
          </div>
          <ConnectedAvatarDisplay
            size="large"
            showName={true}
            showRarity={true}
            className="border-2 border-primary/20 rounded-lg p-2"
          />
        </div>
      </div>

      {/* Main Customization Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Bộ sưu tập Avatar
          </CardTitle>
          <CardDescription>
            Chọn và trang bị các items để tùy chỉnh appearance của bạn
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomizationTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            enableUrlSync={true}
            avatarsContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.owned.avatars.length || 0} /{" "}
                    {(availableItems?.owned.avatars.length || 0) +
                      (availableItems?.unlockable.avatars.length || 0) +
                      (availableItems?.locked.avatars.length || 0)}{" "}
                    avatars
                  </p>
                </div>

                <AvatarGrid
                  ownedAvatars={availableItems?.owned.avatars || []}
                  unlockableAvatars={availableItems?.unlockable.avatars || []}
                  lockedAvatars={availableItems?.locked.avatars || []}
                  equippedAvatarId={equippedAvatar?.avatar_id}
                  onEquipAvatar={handleEquipAvatar}
                  isLoading={isAvailableItemsLoading}
                  isEquipping={isEquipping}
                />
              </div>
            }
            framesContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.owned.frames.length || 0} /{" "}
                    {(availableItems?.owned.frames.length || 0) +
                      (availableItems?.unlockable.frames.length || 0) +
                      (availableItems?.locked.frames.length || 0)}{" "}
                    frames
                  </p>
                </div>

                {/* Frame Grid Component will be implemented in Task 4 */}
                <div className="min-h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <p className="text-muted-foreground">
                    Frame Grid sẽ được triển khai trong Task 4
                  </p>
                </div>
              </div>
            }
            nameEffectsContent={
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {availableItems?.owned.emojis.length || 0} /{" "}
                    {(availableItems?.owned.emojis.length || 0) +
                      (availableItems?.unlockable.emojis.length || 0) +
                      (availableItems?.locked.emojis.length || 0)}{" "}
                    effects
                  </p>
                </div>

                {/* Name Effects Grid Component will be implemented in Task 5 */}
                <div className="min-h-[400px] flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <p className="text-muted-foreground">
                    Name Effects Grid sẽ được triển khai trong Task 5
                  </p>
                </div>
              </div>
            }
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default AvatarCustomizationPage;
