"use client";

import React, { useState, useMemo, useCallback } from "react";
import Image from "next/image";
import { Search, Filter, Check, Lock, Star } from "lucide-react";
import { Input } from "@/components/ui/forms";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Card, CardContent } from "@/components/ui/layout";
import { Skeleton } from "@/components/ui/feedback";
import { cn } from "@/lib/utils";
import { AvatarData, AvatarRarity } from "@/lib/types/avatar";
import { useAvatar } from "@/lib/hooks/use-avatar";

// Item state types for visual indicators
export type ItemState = "owned" | "unlockable" | "locked";

// Props interface for AvatarGrid component
export interface AvatarGridProps {
  // Data
  ownedAvatars: AvatarData[];
  unlockableAvatars: AvatarData[];
  lockedAvatars: AvatarData[];
  
  // Current equipped avatar
  equippedAvatarId?: number;
  
  // Actions
  onEquipAvatar: (avatarId: number) => Promise<void>;
  
  // Loading states
  isLoading?: boolean;
  isEquipping?: boolean;
  
  // Styling
  className?: string;
}

// Individual avatar item component
interface AvatarItemProps {
  avatar: AvatarData;
  state: ItemState;
  isEquipped: boolean;
  isEquipping: boolean;
  onEquip: (avatarId: number) => Promise<void>;
}

const AvatarItem: React.FC<AvatarItemProps> = ({
  avatar,
  state,
  isEquipped,
  isEquipping,
  onEquip,
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Get rarity color
  const { getRarityColor, getRarityDisplayName } = useAvatar();
  const rarityColor = getRarityColor(avatar.rarity);
  const rarityDisplayName = getRarityDisplayName(avatar.rarity);

  // Handle equip action
  const handleEquip = useCallback(async () => {
    if (state === "owned" && !isEquipped && !isEquipping) {
      await onEquip(avatar.avatar_id);
    }
  }, [state, isEquipped, isEquipping, onEquip, avatar.avatar_id]);

  // Get border style based on state
  const getBorderStyle = () => {
    if (isEquipped) return "border-2 border-primary ring-2 ring-primary/20";
    
    switch (state) {
      case "owned":
        return "border-2 border-green-500/50 hover:border-green-500";
      case "unlockable":
        return "border-2 border-yellow-500/50 hover:border-yellow-500";
      case "locked":
        return "border-2 border-gray-300/50";
      default:
        return "border-2 border-border";
    }
  };

  // Get unlock requirement text
  const getUnlockText = () => {
    if (state === "unlockable") {
      return `Cần level ${avatar.unlock_level}`;
    }
    if (state === "locked") {
      return "Chưa mở khóa";
    }
    return null;
  };

  return (
    <Card 
      className={cn(
        "group relative overflow-hidden transition-all duration-200",
        getBorderStyle(),
        state === "owned" && !isEquipped && "hover:shadow-lg cursor-pointer",
        state === "locked" && "opacity-60",
        isEquipping && "pointer-events-none opacity-50"
      )}
      onClick={handleEquip}
    >
      <CardContent className="p-3">
        {/* Avatar Image */}
        <div className="relative aspect-square mb-3">
          {!imageError ? (
            <>
              {imageLoading && (
                <Skeleton className="absolute inset-0 rounded-lg" />
              )}
              <Image
                src={`/avatar-animal-pack/${avatar.image_path}`}
                alt={avatar.name}
                fill
                className={cn(
                  "object-cover rounded-lg transition-opacity duration-300",
                  imageLoading ? "opacity-0" : "opacity-100"
                )}
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                loading="lazy"
                onLoad={() => setImageLoading(false)}
                onError={() => {
                  setImageLoading(false);
                  setImageError(true);
                }}
              />
            </>
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg flex items-center justify-center">
              <span className="text-gray-500 text-2xl">👤</span>
            </div>
          )}

          {/* Status Indicators */}
          <div className="absolute top-2 right-2 flex gap-1">
            {isEquipped && (
              <Badge variant="default" className="text-xs">
                <Check className="h-3 w-3 mr-1" />
                Đang dùng
              </Badge>
            )}
            {state === "locked" && (
              <Badge variant="secondary" className="text-xs">
                <Lock className="h-3 w-3" />
              </Badge>
            )}
          </div>

          {/* Rarity Indicator */}
          <div className="absolute top-2 left-2">
            <Badge 
              variant="outline" 
              className="text-xs"
              style={{ 
                borderColor: rarityColor,
                color: rarityColor,
                backgroundColor: `${rarityColor}10`
              }}
            >
              <Star className="h-3 w-3 mr-1" fill="currentColor" />
              {rarityDisplayName}
            </Badge>
          </div>
        </div>

        {/* Avatar Info */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm truncate">{avatar.name}</h4>
          
          {avatar.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {avatar.description}
            </p>
          )}

          {/* Unlock Requirements */}
          {getUnlockText() && (
            <p className="text-xs text-muted-foreground">
              {getUnlockText()}
            </p>
          )}

          {/* Action Button */}
          {state === "owned" && !isEquipped && (
            <Button
              size="sm"
              className="w-full mt-2"
              disabled={isEquipping}
              onClick={(e) => {
                e.stopPropagation();
                handleEquip();
              }}
            >
              {isEquipping ? "Đang trang bị..." : "Trang bị"}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * AvatarGrid Component
 * 
 * Displays a grid of avatars with:
 * - Visual state indicators (owned/unlockable/locked)
 * - Search and filter functionality
 * - Lazy loading for performance
 * - Responsive grid layout
 */
export const AvatarGrid: React.FC<AvatarGridProps> = ({
  ownedAvatars,
  unlockableAvatars,
  lockedAvatars,
  equippedAvatarId,
  onEquipAvatar,
  isLoading = false,
  isEquipping = false,
  className,
}) => {
  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [rarityFilter, setRarityFilter] = useState<AvatarRarity | "all">("all");

  // Combine all avatars with their states
  const allAvatars = useMemo(() => {
    const avatarsWithState = [
      ...ownedAvatars.map(avatar => ({ avatar, state: "owned" as ItemState })),
      ...unlockableAvatars.map(avatar => ({ avatar, state: "unlockable" as ItemState })),
      ...lockedAvatars.map(avatar => ({ avatar, state: "locked" as ItemState })),
    ];

    return avatarsWithState;
  }, [ownedAvatars, unlockableAvatars, lockedAvatars]);

  // Filter avatars based on search and filters
  const filteredAvatars = useMemo(() => {
    let filtered = allAvatars;

    // Search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      filtered = filtered.filter(({ avatar }) =>
        avatar.name.toLowerCase().includes(searchLower) ||
        (avatar.description && avatar.description.toLowerCase().includes(searchLower))
      );
    }

    // Rarity filter
    if (rarityFilter !== "all") {
      filtered = filtered.filter(({ avatar }) => avatar.rarity === rarityFilter);
    }

    return filtered;
  }, [allAvatars, searchQuery, rarityFilter]);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {Array.from({ length: 10 }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-3">
                <Skeleton className="aspect-square mb-3 rounded-lg" />
                <Skeleton className="h-4 mb-2" />
                <Skeleton className="h-3 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Tìm kiếm avatar..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Rarity Filter */}
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <select
            value={rarityFilter}
            onChange={(e) => setRarityFilter(e.target.value as AvatarRarity | "all")}
            className="px-3 py-2 border border-border rounded-md bg-background text-sm"
          >
            <option value="all">Tất cả độ hiếm</option>
            <option value="COMMON">Thường</option>
            <option value="UNCOMMON">Không phổ biến</option>
            <option value="RARE">Hiếm</option>
            <option value="EPIC">Sử thi</option>
            <option value="LEGENDARY">Huyền thoại</option>
          </select>
        </div>
      </div>

      {/* Avatar Grid */}
      {filteredAvatars.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">Không tìm thấy avatar</p>
            <p className="text-sm">Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc</p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredAvatars.map(({ avatar, state }) => (
            <AvatarItem
              key={avatar.avatar_id}
              avatar={avatar}
              state={state}
              isEquipped={avatar.avatar_id === equippedAvatarId}
              isEquipping={isEquipping}
              onEquip={onEquipAvatar}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AvatarGrid;
