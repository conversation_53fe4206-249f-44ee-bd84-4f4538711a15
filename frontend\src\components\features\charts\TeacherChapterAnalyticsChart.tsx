"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/layout";
import { But<PERSON> } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import { Progress } from "@/components/ui/feedback";
import {
  Loader2,
  BarChart3,
  Users,
  Trophy,
  BookOpen,
  Target,
  AlertCircle,
  CheckCircle,
  Award,
  FileText,
  PenTool,
} from "lucide-react";
import { useComprehensiveQuizReport } from "@/hooks/useTeacherAnalytics";

interface TeacherChapterAnalyticsChartProps {
  quizId: number;
  quizName?: string;
  className?: string;
}

export default function TeacherChapterAnalyticsChart({
  quizId,
  className = "",
}: TeacherChapterAnalyticsChartProps) {
  const { data, loading, error, refetch } = useComprehensiveQuizReport(quizId);

  // Helper functions
  const getPerformanceLevelColor = (level: string) => {
    switch (level) {
      case "excellent":
        return "bg-green-100 text-green-800";
      case "good":
        return "bg-blue-100 text-blue-800";
      case "average":
        return "bg-yellow-100 text-yellow-800";
      case "weak":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800";
      case "improve":
        return "bg-yellow-100 text-yellow-800";
      case "maintain":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "strengths":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "weaknesses":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "recommendations":
        return <Target className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-20">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <span className="text-lg font-medium text-muted-foreground">
              Đang tải dữ liệu phân tích...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-20">
          <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-600 mb-2">
            Lỗi tải dữ liệu
          </p>
          <p className="text-muted-foreground text-center">
            {error || "Không thể tải dữ liệu phân tích"}
          </p>
          <Button onClick={refetch} className="mt-4" variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-6 w-6 text-primary" />
            Tổng quan hiệu suất: {data.quiz_info.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Users className="h-5 w-5 text-blue-600" />
                <span className="text-2xl font-bold text-blue-600">
                  {data.overall_performance.total_participants}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Tổng số học sinh</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Trophy className="h-5 w-5 text-yellow-600" />
                <span className="text-2xl font-bold text-yellow-600">
                  {data.overall_performance.average_score.toFixed(1)}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Điểm trung bình</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-2xl font-bold text-green-600">
                  {data.overall_performance.completion_rate.toFixed(1)}%
                </span>
              </div>
              <p className="text-sm text-muted-foreground">Tỷ lệ hoàn thành</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Student Groups Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-6 w-6 text-primary" />
            Phân bố nhóm học sinh
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {data.student_groups.excellent.count}
              </div>
              <div className="text-sm text-green-700 mb-2">
                ({data.student_groups.excellent.percentage.toFixed(1)}%)
              </div>
              <Badge className="bg-green-100 text-green-800">Xuất sắc</Badge>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {data.student_groups.good.count}
              </div>
              <div className="text-sm text-blue-700 mb-2">
                ({data.student_groups.good.percentage.toFixed(1)}%)
              </div>
              <Badge className="bg-blue-100 text-blue-800">Giỏi</Badge>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600 mb-1">
                {data.student_groups.average.count}
              </div>
              <div className="text-sm text-yellow-700 mb-2">
                ({data.student_groups.average.percentage.toFixed(1)}%)
              </div>
              <Badge className="bg-yellow-100 text-yellow-800">
                Trung bình
              </Badge>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600 mb-1">
                {data.student_groups.weak.count}
              </div>
              <div className="text-sm text-red-700 mb-2">
                ({data.student_groups.weak.percentage.toFixed(1)}%)
              </div>
              <Badge className="bg-red-100 text-red-800">Yếu</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Learning Outcome Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-6 w-6 text-primary" />
            Phân tích Learning Outcomes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.learning_outcome_analysis.map((lo, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg">{lo.lo_name}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-muted-foreground">
                        Độ chính xác: {(lo.accuracy * 100).toFixed(1)}%
                      </span>
                      <Badge
                        className={getPerformanceLevelColor(
                          lo.performance_level
                        )}
                      >
                        {lo.performance_level === "excellent" && "Xuất sắc"}
                        {lo.performance_level === "good" && "Tốt"}
                        {lo.performance_level === "average" && "Trung bình"}
                        {lo.performance_level === "weak" && "Yếu"}
                      </Badge>
                    </div>
                  </div>
                  <div className="text-right">
                    <Progress value={lo.accuracy * 100} className="w-24 h-2" />
                  </div>
                </div>

                {lo.insights.length > 0 && (
                  <div className="mb-3">
                    <h5 className="font-medium text-sm mb-2 flex items-center gap-1">
                      <Target className="h-4 w-4" />
                      Insights:
                    </h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {lo.insights.map((insight, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="text-blue-500 mt-1">•</span>
                          {insight}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {lo.recommendations.length > 0 && (
                  <div>
                    <h5 className="font-medium text-sm mb-2 flex items-center gap-1">
                      <PenTool className="h-4 w-4" />
                      Khuyến nghị:
                    </h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {lo.recommendations.map((rec, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="text-green-500 mt-1">•</span>
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Teacher Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-6 w-6 text-primary" />
            Teacher Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.teacher_insights.map((insight, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-4 border rounded-lg"
              >
                <div className="flex-shrink-0 mt-1">
                  {getCategoryIcon(insight.category)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="font-medium capitalize">
                      {insight.category === "strengths" && "Điểm mạnh"}
                      {insight.category === "weaknesses" && "Điểm yếu"}
                      {insight.category === "recommendations" && "Khuyến nghị"}
                    </span>
                    <Badge className={getPriorityColor(insight.priority)}>
                      {insight.priority === "urgent" && "Khẩn cấp"}
                      {insight.priority === "improve" && "Cần cải thiện"}
                      {insight.priority === "maintain" && "Duy trì"}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {insight.message}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
