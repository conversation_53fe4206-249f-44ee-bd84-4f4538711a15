"use client";

import React from "react";
import QuizComparisonChart from "@/components/features/charts/QuizComparisonChart";
import TeacherChapterAnalyticsChart from "@/components/features/charts/TeacherChapterAnalyticsChart";
import StudentGroupChapterAnalysis from "@/components/features/charts/StudentGroupChapterAnalysis";
import TeachingInsightsCard from "@/components/features/charts/TeachingInsightsCard";

export default function QuizComparisonTest() {
  const testQuizId = 1;

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-2xl font-bold mb-6">
        Teacher Analytics Components Test
      </h1>

      <div className="space-y-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">
            1. Teacher Chapter Analytics
          </h2>
          <TeacherChapterAnalyticsChart quizId={testQuizId} />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">
            2. Student Group Analysis
          </h2>
          <StudentGroupChapterAnalysis quizId={testQuizId} />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">3. Teaching Insights</h2>
          <TeachingInsightsCard quizId={testQuizId} />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">4. Quiz Comparison</h2>
          <QuizComparisonChart quizId={testQuizId} />
        </div>
      </div>
    </div>
  );
}
