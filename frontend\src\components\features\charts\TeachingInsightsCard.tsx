"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/layout";
import { Button } from "@/components/ui/forms";
import { Badge } from "@/components/ui/feedback";
import {
  Loader2,
  Lightbulb,
  Target,
  AlertCircle,
  CheckCircle,
  Award,
  BookOpen,
  Users,
  FileText,
  Download,
  Clock,
} from "lucide-react";
import { showSuccessToast, showErrorToast } from "@/lib/utils/toast-utils";
import { useTeachingInsights } from "@/hooks/useTeacherAnalytics";

interface TeachingInsightsCardProps {
  quizId: number;
  className?: string;
}

export default function TeachingInsightsCard({
  quizId,
  className = "",
}: TeachingInsightsCardProps) {
  const { data, loading, error, refetch } = useTeachingInsights(quizId);

  // Helper functions
  const getAssessmentColor = (assessment: string) => {
    switch (assessment) {
      case "excellent":
        return "bg-green-100 text-green-800 border-green-200";
      case "good":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "mixed":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "needs_improvement":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getAssessmentLabel = (assessment: string) => {
    switch (assessment) {
      case "excellent":
        return "Xuất sắc";
      case "good":
        return "Tốt";
      case "mixed":
        return "Hỗn hợp";
      case "needs_improvement":
        return "Cần cải thiện";
      default:
        return assessment;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "strength":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "weakness":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case "opportunity":
        return <Target className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "curriculum_revision":
        return <BookOpen className="h-4 w-4 text-blue-600" />;
      case "teaching_method":
        return <Award className="h-4 w-4 text-purple-600" />;
      case "student_support":
        return <Users className="h-4 w-4 text-green-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleExportInsights = () => {
    if (!data) return;

    try {
      // Create export data
      const exportData = {
        summary: data.summary_insights,
        detailed_insights: data.detailed_insights,
        generated_at: new Date().toISOString(),
      };

      // Create and download JSON file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `teaching-insights-quiz-${quizId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showSuccessToast("Xuất insights thành công!");
    } catch (error) {
      console.error("Error exporting insights:", error);
      showErrorToast("Không thể xuất insights");
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-20">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <span className="text-lg font-medium text-muted-foreground">
              Đang tải teaching insights...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-20">
          <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-600 mb-2">
            Lỗi tải dữ liệu
          </p>
          <p className="text-muted-foreground text-center">
            {error || "Không thể tải teaching insights"}
          </p>
          <Button onClick={refetch} className="mt-4" variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Summary Insights */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-6 w-6 text-primary" />
              Teaching Insights Summary
            </CardTitle>
            <Button
              onClick={handleExportInsights}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Xuất
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div
            className={`p-4 rounded-lg border-2 ${getAssessmentColor(
              data.summary_insights.overall_assessment
            )}`}
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">
                Đánh giá tổng thể:{" "}
                {getAssessmentLabel(data.summary_insights.overall_assessment)}
              </h3>
              {data.summary_insights.immediate_actions_needed > 0 && (
                <Badge className="bg-red-100 text-red-800">
                  {data.summary_insights.immediate_actions_needed} hành động cần
                  thiết
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* Key Strengths */}
              {data.summary_insights.key_strengths.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Điểm mạnh chính
                  </h4>
                  <ul className="space-y-1">
                    {data.summary_insights.key_strengths.map(
                      (strength, index) => (
                        <li
                          key={index}
                          className="text-sm flex items-start gap-2"
                        >
                          <span className="text-green-500 mt-1">•</span>
                          {strength}
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}

              {/* Main Challenges */}
              {data.summary_insights.main_challenges.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    Thách thức chính
                  </h4>
                  <ul className="space-y-1">
                    {data.summary_insights.main_challenges.map(
                      (challenge, index) => (
                        <li
                          key={index}
                          className="text-sm flex items-start gap-2"
                        >
                          <span className="text-red-500 mt-1">•</span>
                          {challenge}
                        </li>
                      )
                    )}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-6 w-6 text-primary" />
            Insights chi tiết
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Curriculum Insights */}
            {data.detailed_insights.curriculum_insights.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                  Curriculum Insights
                </h4>
                <div className="space-y-3">
                  {data.detailed_insights.curriculum_insights.map(
                    (insight, index) => (
                      <div
                        key={index}
                        className="flex items-start gap-3 p-3 border rounded-lg"
                      >
                        <div className="flex-shrink-0 mt-1">
                          {getTypeIcon(insight.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm capitalize">
                              {insight.type === "strength" && "Điểm mạnh"}
                              {insight.type === "weakness" && "Điểm yếu"}
                              {insight.type === "opportunity" && "Cơ hội"}
                            </span>
                            <Badge className={getImpactColor(insight.impact)}>
                              {insight.impact === "high" && "Tác động cao"}
                              {insight.impact === "medium" &&
                                "Tác động trung bình"}
                              {insight.impact === "low" && "Tác động thấp"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {insight.message}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Teaching Method Insights */}
            {data.detailed_insights.teaching_method_insights.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Award className="h-5 w-5 text-purple-600" />
                  Teaching Method Insights
                </h4>
                <div className="space-y-3">
                  {data.detailed_insights.teaching_method_insights.map(
                    (insight, index) => (
                      <div
                        key={index}
                        className="flex items-start gap-3 p-3 border rounded-lg"
                      >
                        <div className="flex-shrink-0 mt-1">
                          {getTypeIcon(insight.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm capitalize">
                              {insight.type === "strength" && "Điểm mạnh"}
                              {insight.type === "weakness" && "Điểm yếu"}
                              {insight.type === "opportunity" && "Cơ hội"}
                            </span>
                            <Badge className={getImpactColor(insight.impact)}>
                              {insight.impact === "high" && "Tác động cao"}
                              {insight.impact === "medium" &&
                                "Tác động trung bình"}
                              {insight.impact === "low" && "Tác động thấp"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {insight.message}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Student Insights */}
            {data.detailed_insights.student_insights.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Users className="h-5 w-5 text-green-600" />
                  Student Insights
                </h4>
                <div className="space-y-3">
                  {data.detailed_insights.student_insights.map(
                    (insight, index) => (
                      <div
                        key={index}
                        className="flex items-start gap-3 p-3 border rounded-lg"
                      >
                        <div className="flex-shrink-0 mt-1">
                          {getTypeIcon(insight.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm capitalize">
                              {insight.type === "strength" && "Điểm mạnh"}
                              {insight.type === "weakness" && "Điểm yếu"}
                              {insight.type === "opportunity" && "Cơ hội"}
                            </span>
                            <Badge className={getImpactColor(insight.impact)}>
                              {insight.impact === "high" && "Tác động cao"}
                              {insight.impact === "medium" &&
                                "Tác động trung bình"}
                              {insight.impact === "low" && "Tác động thấp"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {insight.message}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Recommendations */}
      {data.detailed_insights.action_recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-6 w-6 text-primary" />
              Khuyến nghị hành động
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.detailed_insights.action_recommendations.map(
                (rec, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-4 border rounded-lg"
                  >
                    <div className="flex-shrink-0 mt-1">
                      {getCategoryIcon(rec.category)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-sm">
                          {rec.category === "curriculum_revision" &&
                            "Cải tiến Curriculum"}
                          {rec.category === "teaching_method" &&
                            "Phương pháp Giảng dạy"}
                          {rec.category === "student_support" &&
                            "Hỗ trợ Học sinh"}
                        </span>
                        <Badge className={getPriorityColor(rec.priority)}>
                          {rec.priority === "high" && "Ưu tiên cao"}
                          {rec.priority === "medium" && "Ưu tiên trung bình"}
                          {rec.priority === "low" && "Ưu tiên thấp"}
                        </Badge>
                        <Badge variant="outline">
                          {rec.timeline === "immediate" && "Ngay lập tức"}
                          {rec.timeline === "short_term" && "Ngắn hạn"}
                          {rec.timeline === "long_term" && "Dài hạn"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {rec.action}
                      </p>
                    </div>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
