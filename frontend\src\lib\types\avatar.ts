// Avatar system TypeScript interfaces

// Rarity levels for avatar items
export type AvatarRarity = "COMMON" | "UNCOMMON" | "RARE" | "EPIC" | "LEGENDARY";

// Unlock types for avatar items
export type UnlockType = "LEVEL" | "ACHIEVEMENT" | "PURCHASE" | "SPECIAL_EVENT";

// Item types for avatar customization
export type ItemType = "avatar" | "frame" | "name_effect";

// Base Avatar Data Interface
export interface AvatarData {
  avatar_id: number;
  name: string;
  description: string;
  image_path: string;
  rarity: AvatarRarity;
  unlock_type: UnlockType;
  unlock_level: number;
}

// Avatar Frame Interface
export interface AvatarFrame {
  frame_id: number;
  name: string;
  description: string;
  image_path: string;
  rarity: AvatarRarity;
  unlock_type: UnlockType;
  unlock_level: number;
}

// Name Effect Interface
export interface NameEffect {
  effect_id: number;
  name: string;
  description: string;
  css_class: string;
  rarity: AvatarRarity;
  unlock_type: UnlockType;
  unlock_level: number;
}

// Emoji Data Interface
export interface EmojiData {
  emoji_id: number;
  name: string;
  unicode: string;
  category: string;
  rarity: AvatarRarity;
  unlock_type: UnlockType;
  unlock_level: number;
}

// Inventory Item Interfaces
export interface AvatarInventoryItem {
  user_inventory_id: number;
  item_id: number;
  item_type: "avatar";
  unlocked_at: string;
  Avatar: AvatarData;
}

export interface FrameInventoryItem {
  user_inventory_id: number;
  item_id: number;
  item_type: "frame";
  unlocked_at: string;
  Frame: AvatarFrame;
}

export interface EmojiInventoryItem {
  user_inventory_id: number;
  item_id: number;
  item_type: "emoji";
  unlocked_at: string;
  Emoji: EmojiData;
}

// User Inventory Interface
export interface UserInventory {
  avatars: AvatarInventoryItem[];
  frames: FrameInventoryItem[];
  emojis: EmojiInventoryItem[];
}

// User Customization Interface
export interface UserCustomization {
  equipped_avatar_id: number;
  equipped_frame_id: number;
  equipped_name_effect_id: number;
}

// Collection Progress Interface
export interface CollectionProgress {
  avatars: {
    total_available: number;
    unlocked: number;
    completion_rate: string;
  };
  frames: {
    total_available: number;
    unlocked: number;
    completion_rate: string;
  };
  emojis: {
    total_available: number;
    unlocked: number;
    completion_rate: string;
  };
  overall: {
    total_items: number;
    unlocked_items: number;
    completion_rate: string;
  };
}

// API Response Interfaces
export interface MyAvatarDataResponse {
  success: boolean;
  data: {
    customization: UserCustomization;
    inventory: UserInventory;
    statistics: {
      total_items: number;
      completion_rate: string;
    };
    user_level: number;
    user_tier: string;
  };
  message?: string;
}

export interface AvailableItemsResponse {
  success: boolean;
  data: {
    owned: {
      avatars: AvatarData[];
      frames: AvatarFrame[];
      emojis: EmojiData[];
    };
    unlockable: {
      avatars: AvatarData[];
      frames: AvatarFrame[];
      emojis: EmojiData[];
    };
    locked: {
      avatars: AvatarData[];
      frames: AvatarFrame[];
      emojis: EmojiData[];
    };
  };
  message?: string;
}

export interface EquipItemRequest {
  itemType: ItemType;
  itemId: number;
}

export interface EquipItemResponse {
  success: boolean;
  data: {
    customization: UserCustomization;
  };
  message?: string;
}

export interface CollectionProgressResponse {
  success: boolean;
  data: CollectionProgress;
  message?: string;
}
